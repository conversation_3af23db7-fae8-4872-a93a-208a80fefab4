import { createShadowRootUi } from 'wxt/client';

interface FormField {
  element: HTMLElement;
  type: string;
  id: string;
  name: string;
  placeholder?: string;
  label?: string;
}

interface LoginStatus {
  isLoggedIn: boolean;
  skipLogin: boolean;
}

interface UserInfo {
  user?: {
    name?: string;
    picture_url?: string;
  };
}

interface PopupConfig {
  mode: string;
  language: string;
  projects: Array<{ id: string; name: string; }>;
  hasFormFields: boolean;
}



export class InPagePopup {
  private ui: Awaited<ReturnType<typeof createShadowRootUi>> | null = null;
  private ctx: any; // ContentScriptContext
  private isDragging = false;
  private dragStartX = 0;
  private dragStartY = 0;
  private popupStartX = 0;
  private popupStartY = 0;
  private longPressTimer: number | null = null;
  private isLongPressTriggered = false;
  private animationFrame: number | null = null;
  private reasoningBubble: HTMLElement | null = null;

  constructor(ctx: any) {
    this.ctx = ctx;
    this.setupStreamingListeners();
  }

  /**
   * 设置流式处理监听器
   */
  private setupStreamingListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === 'streamingUpdate') {
        this.handleStreamingUpdate(message);
      } else if (message.type === 'streamingComplete') {
        this.handleStreamingComplete(message);
      }
    });
  }

  /**
   * 处理流式更新 - 增量内容更新
   */
  public handleStreamingUpdate(message: any) {
    console.log('[InPagePopup] Streaming update received:', {
      contentLength: message.content?.length || 0,
      reasoningLength: message.reasoning?.length || 0,
      isComplete: message.isComplete
    });

    // 实时更新推理气泡内容（如果有推理内容）
    if (message.reasoning) {
      this.updateReasoningBubble(message.reasoning, false);
    }

    // 更新生成状态显示（显示当前内容长度）
    if (message.content) {
      this.updateGeneratingStatus(`Generated ${message.content.length} characters...`, false);
    }
  }

  /**
   * 处理流式完成
   */
  public async handleStreamingComplete(message: any) {
    console.log('[InPagePopup] Streaming complete:', message);

    if (!this.ui) return;
    const container = this.ui.uiContainer;

    try {
      // 最终更新推理气泡内容
      if (message.reasoning) {
        this.updateReasoningBubble(message.reasoning, true); // true 表示完成状态
      }

      // 填充表单
      if (message.data) {
        await this.fillForm(message.data);

        // 成功状态
        const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
        const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
        const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
        const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;

        if (generateBtn && btnText) {
          generateBtn.classList.remove('loading');
          generateBtn.classList.add('success');
          btnText.textContent = 'Finish';
          if (loadingAnimation) loadingAnimation.style.display = 'none';
          if (sparkleIcon) sparkleIcon.classList.remove('hidden');
        }

        // 显示成功消息
        this.showStatusMessage('Form filled successfully!', 'success');

        // 显示彩带效果
        this.createConfetti(container);

        // 延迟隐藏推理气泡，但不自动关闭弹窗
        setTimeout(() => {
          this.hideReasoningBubble();
          // 移除自动关闭弹窗的逻辑，让用户手动关闭
        }, 3000); // 给用户更多时间查看推理内容
      }
    } catch (error) {
      console.error('[InPagePopup] Error in streaming complete:', error);
      this.showStatusMessage(error instanceof Error ? error.message : 'An error occurred', 'error');

      // 恢复按钮状态
      this.updateGeneratingStatus('', true);
    } finally {
      // 清除生成效果
      this.removeGeneratingEffect();
    }
  }

  /**
   * 创建推理气泡
   */
  private createReasoningBubble(): HTMLElement {
    const bubble = document.createElement('div');
    bubble.className = 'fillify-reasoning-bubble';
    bubble.style.cssText = `
      position: fixed;
      z-index: 2147483646;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 16px;
      padding: 16px 20px;
      height: 280px;
      overflow-y: auto;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 13px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
      opacity: 0;
      transform: scale(0.8) translateY(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: auto;
    `;

    // 添加标题
    const header = document.createElement('div');
    header.style.cssText = `
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      font-weight: 600;
      font-size: 14px;
    `;

    const icon = document.createElement('div');
    icon.innerHTML = '🤔';
    icon.style.cssText = `
      margin-right: 8px;
      font-size: 16px;
    `;

    const title = document.createElement('span');
    title.textContent = 'AI Thinking...';

    header.appendChild(icon);
    header.appendChild(title);

    // 添加内容区域
    const content = document.createElement('div');
    content.className = 'fillify-reasoning-content';
    content.style.cssText = `
      font-size: 13px;
      line-height: 1.5;
      color: rgba(255, 255, 255, 0.9);
      height: 200px;
      overflow-y: auto;
      margin-top: 8px;
      padding-right: 8px;
      scrollbar-width: thin;
      scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    `;

    // 添加自定义滚动条样式（WebKit）
    const scrollbarStyle = document.createElement('style');
    scrollbarStyle.textContent = `
      .fillify-reasoning-content::-webkit-scrollbar {
        width: 6px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
      }
      .fillify-reasoning-content::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    `;
    bubble.appendChild(scrollbarStyle);

    // 添加箭头指示器
    const arrow = document.createElement('div');
    arrow.className = 'fillify-reasoning-arrow';
    arrow.style.cssText = `
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
    `;

    bubble.appendChild(header);
    bubble.appendChild(content);
    bubble.appendChild(arrow);

    return bubble;
  }

  /**
   * 更新推理气泡内容
   */
  private updateReasoningBubble(reasoning: string, isComplete: boolean = false) {
    if (!this.ui) return;

    // 如果气泡不存在，创建它
    if (!this.reasoningBubble) {
      this.reasoningBubble = this.createReasoningBubble();
      document.body.appendChild(this.reasoningBubble);

      // 计算位置
      this.positionReasoningBubble();

      // 显示动画
      requestAnimationFrame(() => {
        if (this.reasoningBubble) {
          this.reasoningBubble.style.opacity = '1';
          this.reasoningBubble.style.transform = 'scale(1) translateY(0)';
        }
      });
    } else {
      // 如果气泡已存在，重新计算位置（防止 popup 被拖拽后位置变化）
      this.positionReasoningBubble();
    }

    // 更新内容
    const content = this.reasoningBubble.querySelector('.fillify-reasoning-content') as HTMLElement;
    const header = this.reasoningBubble.querySelector('span') as HTMLElement;

    if (content) {
      content.textContent = reasoning;

      // 使用 requestAnimationFrame 确保滚动在内容更新后执行
      requestAnimationFrame(() => {
        if (content) {
          content.scrollTop = content.scrollHeight; // 自动滚动到底部
        }
      });
    }

    // 更新标题状态
    if (header) {
      if (isComplete) {
        header.textContent = 'AI Thinking Complete ✨';
        const icon = this.reasoningBubble.querySelector('div') as HTMLElement;
        if (icon) icon.innerHTML = '✨';
      } else {
        header.textContent = 'AI Thinking...';
      }
    }
  }

  /**
   * 计算推理气泡的位置
   */
  private positionReasoningBubble() {
    if (!this.ui || !this.reasoningBubble) return;

    const popupElement = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
    if (!popupElement) return;

    const popupRect = popupElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 固定气泡高度和间距
    const bubbleHeight = 280; // 固定高度
    const spacing = 16; // 与 popup 的固定间距

    // 计算是否有足够空间在上方显示
    const spaceAbove = popupRect.top;
    const spaceBelow = viewportHeight - popupRect.bottom;

    let top: number;
    let arrowPosition: 'top' | 'bottom';

    // 优先在上方显示
    if (spaceAbove >= bubbleHeight + spacing + 20) {
      // 在上方显示
      top = popupRect.top - bubbleHeight - spacing;
      arrowPosition = 'bottom';
    } else if (spaceBelow >= bubbleHeight + spacing + 20) {
      // 在下方显示
      top = popupRect.bottom + spacing;
      arrowPosition = 'top';
    } else {
      // 空间不足，选择空间较大的一侧
      if (spaceAbove > spaceBelow) {
        top = Math.max(20, popupRect.top - bubbleHeight - spacing);
        arrowPosition = 'bottom';
      } else {
        top = popupRect.bottom + spacing;
        arrowPosition = 'top';
      }
    }

    // 宽度与 popup 一致，左对齐
    const bubbleWidth = popupRect.width;
    let left = popupRect.left;

    // 确保不超出视口边界
    if (left < 20) left = 20;
    if (left + bubbleWidth > viewportWidth - 20) {
      left = Math.max(20, viewportWidth - bubbleWidth - 20);
    }

    // 应用位置和尺寸
    this.reasoningBubble.style.top = `${top}px`;
    this.reasoningBubble.style.left = `${left}px`;
    this.reasoningBubble.style.width = `${bubbleWidth}px`;

    // 设置箭头位置
    const arrow = this.reasoningBubble.querySelector('.fillify-reasoning-arrow') as HTMLElement;
    if (arrow) {
      // 箭头指向 popup 的中心
      const arrowLeft = popupRect.left + popupRect.width / 2 - left - 8; // 8 是箭头宽度的一半
      const maxArrowLeft = bubbleWidth - 32; // 确保箭头不超出气泡边界

      if (arrowPosition === 'top') {
        arrow.style.cssText += `
          top: -8px;
          left: ${Math.max(16, Math.min(arrowLeft, maxArrowLeft))}px;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-bottom: 8px solid #667eea;
        `;
      } else {
        arrow.style.cssText += `
          bottom: -8px;
          left: ${Math.max(16, Math.min(arrowLeft, maxArrowLeft))}px;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 8px solid #764ba2;
        `;
      }
    }
  }

  /**
   * 隐藏推理气泡
   */
  private hideReasoningBubble() {
    if (this.reasoningBubble) {
      this.reasoningBubble.style.opacity = '0';
      this.reasoningBubble.style.transform = 'scale(0.8) translateY(10px)';

      setTimeout(() => {
        if (this.reasoningBubble && this.reasoningBubble.parentNode) {
          this.reasoningBubble.parentNode.removeChild(this.reasoningBubble);
          this.reasoningBubble = null;
        }
      }, 300);
    }
  }

  /**
   * 更新生成状态
   */
  private updateGeneratingStatus(delta: string, isComplete: boolean) {
    if (!this.ui) return;

    const container = this.ui.uiContainer;
    if (!container) return;

    // 查找生成按钮
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = generateBtn?.querySelector('.fillify-button-text') as HTMLElement;
    const loadingAnimation = generateBtn?.querySelector('.fillify-animation') as HTMLElement;
    const sparkleIcon = generateBtn?.querySelector('.fillify-sparkle-icon') as HTMLElement;

    if (!generateBtn || !btnText) return;

    if (isComplete) {
      generateBtn.disabled = false;
      generateBtn.classList.remove('loading');
      btnText.textContent = 'Generate';
      if (sparkleIcon) sparkleIcon.classList.remove('hidden');
      if (loadingAnimation) loadingAnimation.style.display = 'none';
    } else {
      generateBtn.disabled = true;
      generateBtn.classList.add('loading');
      btnText.textContent = 'Generating...';
      if (sparkleIcon) sparkleIcon.classList.add('hidden');
      if (loadingAnimation) loadingAnimation.style.display = 'block';
    }
  }

  async show() {
    if (this.ui) {
      // UI 已经创建，直接显示
      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (popup) {
        popup.style.display = 'block';
        popup.style.animation = 'slideInFromBottom 0.3s ease-out forwards';
      }
      return;
    }

    try {
      // 获取必要的数据
      const [loginStatus, storage, formFields] = await Promise.all([
        chrome.runtime.sendMessage({ type: 'getLoginStatus' }),
        chrome.storage.sync.get(['formify_last_mode', 'formify_projects', 'formify_last_language']),
        this.detectFormFields()
      ]);

      const userInfo = loginStatus.isLoggedIn 
        ? await chrome.runtime.sendMessage({ type: 'getUserInfo' }) 
        : null;

      const config: PopupConfig = {
        mode: storage.formify_last_mode || 'general',
        language: storage.formify_last_language || 'auto',
        projects: storage.formify_projects || [],
        hasFormFields: formFields.length > 0
      };

      // 创建 Shadow Root UI - 使用传入的 context
      this.ui = await createShadowRootUi(this.ctx, {
        name: 'fillify-popup',
        position: 'inline',
        onMount: (uiContainer, shadow, shadowHost) => {
          this.createPopupContent(uiContainer, loginStatus, userInfo, config);
          this.bindEvents(uiContainer, loginStatus, userInfo, config);
          this.setupDragFunctionality(uiContainer);
          return true;
        },
        onRemove: () => {
          this.cleanup();
        }
      });
      
      this.ui.mount();
    } catch (error) {
      console.error('[InPagePopup] Error creating Shadow Root UI:', error);
      throw error;
    }
  }

  hide() {
    // 隐藏推理气泡
    this.hideReasoningBubble();

    if (this.ui) {
      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (popup) {
        // 添加退出动画
        popup.style.animation = 'slideOutToBottom 0.3s ease-in forwards';

        // 动画完成后移除popup
        setTimeout(() => {
          if (this.ui) {
            this.ui.remove();
            this.ui = null;
          }
        }, 300);
      } else {
        // 如果没有找到popup元素，直接移除
        this.ui.remove();
        this.ui = null;
      }
    }
  }

  toggle() {
    if (this.ui) {
      this.hide();
    } else {
      this.show();
    }
  }

  private createPopupContent(
    container: HTMLElement, 
    loginStatus: LoginStatus, 
    userInfo: UserInfo | null, 
    config: PopupConfig
  ) {
    // 添加样式
    const style = document.createElement('style');
    style.textContent = this.getStyles();
    container.appendChild(style);

    // 创建主容器
    const popup = document.createElement('div');
    popup.className = 'fillify-popup';
    popup.innerHTML = this.getPopupHTML(loginStatus, userInfo, config);
    
    container.appendChild(popup);
  }

  private getStyles(): string {
    return `
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .fillify-popup {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 380px;
        min-height: 370px;
        background: #f5f5f5;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        overflow: hidden;
        z-index: 10001;
        animation: slideInFromBottom 0.3s ease-out;
      }

      @keyframes slideInFromBottom {
        from {
          transform: translateY(100%);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }

      @keyframes slideOutToBottom {
        from {
          transform: translateY(0);
          opacity: 1;
        }
        to {
          transform: translateY(100%);
          opacity: 0;
        }
      }

      /* Logo animations */
      .fillify-login-logo path[d*="M245.625"] {
        transform-box: fill-box;
        transform-origin: center;
        animation: starPulse 2s ease-in-out infinite;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(1) {
        animation-delay: 0s;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(2) {
        animation-delay: -0.6s;
      }

      .fillify-login-logo path[d*="M245.625"]:nth-of-type(3) {
        animation-delay: -1.2s;
      }

      .fillify-login-logo rect[x="340.211"] {
        transform-box: fill-box;
        transform-origin: left;
        animation: rectStretch 3s ease-in-out infinite;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(1) {
        animation-delay: 0s;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(2) {
        animation-delay: -1s;
      }

      .fillify-login-logo rect[x="340.211"]:nth-of-type(3) {
        animation-delay: -2s;
      }

      @keyframes starPulse {
        0%, 100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.15);
        }
      }

      @keyframes rectStretch {
        0%, 100% {
          transform: scaleX(1);
        }
        50% {
          transform: scaleX(1.1);
        }
      }

      /* Header styles */
      .fillify-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 20px;
        background: #f5f5f5;
      }

      .fillify-header h1 {
        margin: 0;
        color: #2962FF;
        font-size: 22px;
      }

      .fillify-header-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      /* Avatar and menu styles */
      .fillify-avatar-menu {
        position: relative;
        cursor: pointer;
      }

      .fillify-avatar-menu img {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        cursor: pointer;
      }

      .fillify-menu-popover {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(-10px);
        margin-top: 4px;
        background: white;
        border: 1px solid #eee;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        min-width: 100px;
        opacity: 0;
        visibility: hidden;
        pointer-events: none;
        transition: all 0.2s ease;
        padding: 2px;
      }

      .fillify-menu-popover::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        right: 0;
        height: 4px;
        background: transparent;
      }

      .fillify-menu-popover::after {
        content: '';
        position: absolute;
        top: -3px;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid white;
      }

      .fillify-avatar-menu:hover .fillify-menu-popover,
      .fillify-menu-popover:hover {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
        pointer-events: auto;
      }

      .fillify-menu-item {
        padding: 6px 12px;
        font-size: 13px;
        color: #dc2626;
        transition: all 0.2s ease;
        cursor: pointer;
        margin: 2px;
        border-radius: 4px;
      }

      .fillify-menu-item:hover {
        background-color: #f5f5f5;
      }

      /* Button styles */
      button {
        background: none;
        border: none;
        cursor: pointer;
        font-family: inherit;
      }

      .fillify-settings-button {
        padding: 8px;
        border-radius: 4px;
        transition: background 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .fillify-settings-button:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      #fillify-close-btn {
        padding: 4px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s ease;
      }

      #fillify-close-btn:hover {
        background: rgba(0, 0, 0, 0.1);
      }

      /* Form styles */
      .fillify-form-container {
        background: white;
        border-radius: 16px;
        margin: 0 12px 12px 12px;
        padding: 20px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        gap: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      }

      /* Mode buttons */
      .fillify-mode-buttons {
        position: relative;
        display: flex;
        gap: 4px;
        padding: 4px;
        background: #f5f5f5;
        border-radius: 8px;
      }

      .fillify-mode-buttons::after {
        content: '';
        position: absolute;
        top: 4px;
        left: 4px;
        width: calc((100% - 16px) / 3);
        height: calc(100% - 8px);
        background: white;
        border-radius: 6px;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        z-index: 0;
      }

      .fillify-mode-btn {
        flex: 1;
        padding: 8px 12px;
        border: none;
        background: transparent;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 1;
        user-select: none;
      }

      .fillify-mode-btn.active {
        color: #2962FF;
      }

      .fillify-mode-btn:hover {
        background: rgba(41, 98, 255, 0.1);
      }

      .fillify-mode-btn.active:hover {
        background: transparent;
      }

      .fillify-mode-btn:active {
        transform: scale(0.97);
      }

      /* Input styles */
      input, textarea, select {
        font-family: inherit;
        box-sizing: border-box;
      }

      textarea {
        width: 100%;
        padding: 14px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.5;
        resize: none;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background-color: #fff;
        color: #333;
        cursor: text;
        opacity: 1;
      }

      textarea:focus {
        outline: none;
        border-color: #2962FF;
        box-shadow: 0 0 0 3px rgba(41, 98, 255, 0.1);
      }

      select {
        padding: 10px 14px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fff;
        font-size: 14px;
        color: #333;
        cursor: pointer;
        appearance: none;
        background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="%23999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"%3E%3Cpolyline points="6 9 12 15 18 9"%3E%3C/polyline%3E%3C/svg%3E');
        background-repeat: no-repeat;
        background-position: right 12px center;
        background-size: 16px;
      }

      select:focus {
        outline: none;
        border-color: #2962FF;
      }

      /* Primary button */
      .fillify-primary-btn {
        width: 100%;
        padding: 8px;
        border: none;
        border-radius: 8px;
        background: #2962FF;
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-height: 40px;
      }

      .fillify-primary-btn:hover:not(.success):not(.loading):not(:disabled) {
        background: #1E4EE3;
      }

      .fillify-primary-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        background: #ccc;
      }

      .fillify-primary-btn.loading {
        background: #1E4EE3;
        cursor: not-allowed;
      }

      .fillify-primary-btn.success {
        background: #2962FF;
        cursor: default;
        color: white;
      }

      /* Sparkle animation */
      @keyframes sparkle {
        0%, 100% {
          transform: scale(1);
          opacity: 0.9;
        }
        50% {
          transform: scale(1.1);
          opacity: 1;
        }
      }

      .fillify-sparkle-icon {
        width: 16px;
        height: 16px;
        color: inherit;
        opacity: 0.9;
        animation: sparkle 2s ease-in-out infinite;
        margin-left: 4px;
        transition: opacity 0.3s ease;
        transform-box: fill-box;
        transform-origin: center;
      }

      .fillify-sparkle-icon.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .fillify-primary-btn:hover .fillify-sparkle-icon {
        animation: sparkle-hover 1s ease-in-out infinite;
      }

      @keyframes sparkle-hover {
        0%, 100% {
          transform: scale(1.1) rotate(0deg);
          opacity: 1;
        }
        50% {
          transform: scale(1.2) rotate(10deg);
          opacity: 0.9;
        }
      }

      /* Loading animation */
      .fillify-primary-btn .fillify-animation {
        display: none;
        position: absolute;
        border-radius: 100%;
        animation: ripple 0.6s linear infinite;
      }

      .fillify-primary-btn.loading .fillify-animation {
        display: block;
      }

      @keyframes ripple {
        0% {
          box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1),
                     0 0 0 40px rgba(255, 255, 255, 0.1),
                     0 0 0 80px rgba(255, 255, 255, 0.1),
                     0 0 0 120px rgba(255, 255, 255, 0.1);
        }
        100% {
          box-shadow: 0 0 0 40px rgba(255, 255, 255, 0.1),
                     0 0 0 80px rgba(255, 255, 255, 0.1),
                     0 0 0 120px rgba(255, 255, 255, 0.1),
                     0 0 0 160px rgba(255, 255, 255, 0);
        }
      }

      /* Status message */
      .fillify-status-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        pointer-events: none;
        z-index: 10002;
      }

      .fillify-status-message {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        max-width: 80%;
        text-align: center;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .fillify-status-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .fillify-status-message.error {
        background: rgba(244, 67, 54, 0.9);
      }

      .fillify-status-message.success {
        background: rgba(76, 175, 80, 0.9);
      }

      .fillify-status-message.info {
        background: rgba(33, 150, 243, 0.9);
      }

      /* Disabled states */
      textarea:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
        opacity: 0.6;
      }

      select:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
      }

      /* Login prompt styles */
      .fillify-login-prompt {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.98);
        min-height: 370px;
      }

      .fillify-login-content {
        text-align: center;
        padding: 30px 15px;
        border-radius: 12px;
        background: white;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 320px;
      }

      .fillify-sign-in-btn {
        background: #1D5DF4;
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        margin-bottom: 12px;
      }

      .fillify-sign-in-btn:hover {
        background: #1850D8;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(29, 93, 244, 0.2);
      }

      .fillify-sign-in-btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(29, 93, 244, 0.2);
      }

      .fillify-skip-login-text {
        display: block;
        color: #666;
        font-size: 14px;
        text-decoration: none;
        margin-top: 12px;
        cursor: pointer;
        transition: color 0.2s ease;
      }

      .fillify-skip-login-text:hover {
        color: #1D5DF4;
        text-decoration: underline;
      }

      /* Language selector */
      .fillify-language-selector {
        margin: 0;
        margin-bottom: -8px;
        display: flex;
        justify-content: flex-end;
      }

      .fillify-language-flex {
        position: relative;
        display: flex;
        align-items: center;
        gap: 2px;
        padding: 0 4px;
        height: 26px;
        border-radius: 4px;
        cursor: pointer;
      }

      .fillify-selected-language {
        color: #666;
        font-size: 12px;
        margin-right: 2px;
      }

      /* Project selector */
      .fillify-project-selector {
        margin: 0;
      }

      .fillify-select-wrapper {
        display: flex;
        gap: 10px;
        align-items: center;
      }

      .fillify-add-project-btn {
        padding: 10px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background: #fff;
        color: #666;
        font-size: 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 42px;
        transition: all 0.2s ease;
      }

      .fillify-add-project-btn:hover {
        background: #f5f5f5;
        border-color: #ccc;
      }

      /* Confetti canvas */
      .fillify-confetti-canvas {
        position: absolute;
        top: 0;
        left: 0;
        pointer-events: none;
        z-index: 1000;
        width: 100%;
        height: 100%;
      }




    `;
  }

  private getPopupHTML(loginStatus: LoginStatus, userInfo: UserInfo | null, config: PopupConfig): string {
    const isLoggedIn = loginStatus?.isLoggedIn || false;
    const skipLogin = loginStatus?.skipLogin || false;
    const showLoginPrompt = !isLoggedIn && !skipLogin;

    if (showLoginPrompt) {
      return this.getLoginPromptHTML();
    } else {
      return this.getMainPopupHTML(isLoggedIn, userInfo, config);
    }
  }

  private getLoginPromptHTML(): string {
    return `
      <div class="fillify-login-prompt">
        <div class="fillify-login-content">
          <div class="fillify-logo-container" style="margin-bottom: 1rem;">
            <svg class="fillify-login-logo" width="120" height="120" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 120px; height: 120px; margin-bottom: 1rem;">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M814.311 75H125C97.3858 75 75 97.3858 75 125V918C75 945.614 97.3858 968 125 968H905.481C933.096 968 955.481 945.614 955.481 918V216.17L814.311 75Z" fill="#1D5DF4"/>
              <g style="mix-blend-mode:hard-light">
                <path d="M956 217H814V75L885 146L956 217Z" fill="#D9D9D9"/>
              </g>
              <rect x="340.211" y="344.847" width="504.457" height="81.6033" fill="white"/>
              <rect x="340.211" y="508.054" width="504.457" height="81.6033" fill="white"/>
              <rect x="340.211" y="671.261" width="504.457" height="81.6033" fill="white"/>
              <path d="M245.625 333.72L260.152 372.977L299.409 387.504L260.152 402.03L245.625 441.288L231.099 402.03L191.841 387.504L231.099 372.977L245.625 333.72Z" fill="white"/>
              <path d="M245.625 496.926L260.152 536.184L299.409 550.71L260.152 565.237L245.625 604.494L231.099 565.237L191.841 550.71L231.099 536.184L245.625 496.926Z" fill="white"/>
              <path d="M245.625 660.133L260.152 699.39L299.409 713.917L260.152 728.443L245.625 767.701L231.099 728.443L191.841 713.917L231.099 699.39L245.625 660.133Z" fill="white"/>
            </svg>
          </div>
          <h2 style="color: #1D5DF4; font-size: 1.5rem; margin: 0 0 0.5rem; font-weight: 600;">Welcome to Fillify</h2>
          <p style="color: #666; margin: 0 0 1.5rem; font-size: 0.9rem;">Please sign in to access all features</p>
          <button id="fillify-signin-btn" class="fillify-sign-in-btn">Sign in</button>
          <a href="#" id="fillify-skip-login" class="fillify-skip-login-text">Skip sign in</a>
        </div>
      </div>
    `;
  }

  private getMainPopupHTML(isLoggedIn: boolean, userInfo: UserInfo | null, config: PopupConfig): string {
    const userAvatarUrl = userInfo?.user?.picture_url || '';
    const languageMap: Record<string, string> = {
      'auto': 'Auto',
      'id': 'Bahasa Indonesia',
      'ms': 'Bahasa Melayu',
      'da': 'Dansk',
      'de': 'Deutsch',
      'en': 'English',
      'es': 'Español',
      'fr': 'Français',
      'it': 'Italiano',
      'nl': 'Nederlands',
      'no': 'Norsk',
      'pl': 'Polski',
      'pt': 'Português',
      'ro': 'Română',
      'fi': 'Suomi',
      'sv': 'Svenska',
      'vi': 'Tiếng Việt',
      'tr': 'Türkçe',
      'hu': 'Magyar',
      'cs': 'Čeština',
      'uk': 'Українська',
      'ru': 'Русский',
      'bg': 'Български',
      'ar': 'العربية',
      'fa': 'فارسی',
      'he': 'עִבְרִית',
      'hi': 'हिन्दी',
      'th': 'ไทย',
      'ja': '日本語',
      'zh-CN': '中文（简体）',
      'zh-TW': '中文（繁體）',
      'ko': '한국어'
    };

    const selectedLanguageText = languageMap[config.language] || config.language;

    const getPlaceholder = (mode: string) => {
      switch (mode) {
        case 'bugReport': return 'Enter bug description';
        case 'email': return 'Enter email content description';
        default: return 'Enter description';
      }
    };

    return `
      <div class="fillify-container">

        <!-- Header -->
        <div class="fillify-header">
          <h1>Fillify</h1>
          <div class="fillify-header-right">
            <div class="fillify-login-status" style="display: flex; align-items: center; margin-right: 4px; font-size: 14px; color: #666;">
              <div class="fillify-avatar-menu">
                ${isLoggedIn ? `
                  <img src="${userAvatarUrl}" title="Go to Dashboard" id="fillify-avatar" />
                  <div class="fillify-menu-popover">
                    <div class="fillify-menu-item" id="fillify-signout">Sign out</div>
                  </div>
                ` : `
                  <div style="cursor: pointer;" title="Sign in" id="fillify-signin-icon">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" style="vertical-align: middle;">
                      <path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64zM384.8 376c4-64 56-115.2 120-119.2 74.4-4 135.2 55.2 135.2 128 0 70.4-57.6 128-128 128-73.6 0-132-62.4-127.2-136.8zM768 746.4c0 12-9.6 21.6-21.6 21.6H278.4c-12 0-21.6-9.6-21.6-21.6v-64c0-84.8 170.4-128 255.2-128 84.8 0 255.2 42.4 255.2 128l0.8 64z" fill="#333333"/>
                    </svg>
                  </div>
                `}
              </div>
            </div>
            <button class="fillify-settings-button" id="fillify-settings" title="Settings">
              <div class="fillify-menu-icon" style="width: 18px; height: 14px; position: relative; display: flex; flex-direction: column; justify-content: space-between; pointer-events: none;">
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
                <span style="display: block; width: 100%; height: 2px; background-color: #666; border-radius: 2px; transition: all 0.2s ease; pointer-events: none;"></span>
              </div>
            </button>
            <button id="fillify-close-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="#666">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Form container -->
        <form id="fillify-form-config" class="fillify-form-container">
          <!-- Mode buttons -->
          <div class="fillify-mode-buttons" style="background: #f5f5f5;">
            <button type="button" class="fillify-mode-btn ${config.mode === 'general' ? 'active' : ''}" data-mode="general">General</button>
            <button type="button" class="fillify-mode-btn ${config.mode === 'email' ? 'active' : ''}" data-mode="email">Email</button>
            <button type="button" class="fillify-mode-btn ${config.mode === 'bugReport' ? 'active' : ''}" data-mode="bugReport">Bug Report</button>
          </div>

          <!-- Project selector (only for Bug Report mode) -->
          <div class="fillify-project-selector" style="display: ${config.mode === 'bugReport' ? 'block' : 'none'};">
            <div class="fillify-select-wrapper">
              <select id="fillify-project-select" style="flex: 1;" ${!config.hasFormFields ? 'disabled' : ''}>
                <option value="">Select any project (Optional)</option>
                ${config.projects.map(project => `
                  <option value="${project.id}">${project.name}</option>
                `).join('')}
              </select>
              <button type="button" class="fillify-add-project-btn" id="fillify-add-project">+</button>
            </div>
          </div>

          <!-- Description input -->
          <div class="fillify-description-input" style="flex-grow: 1; display: flex; flex-direction: column; gap: 0;">
            <div class="fillify-textarea-wrapper" style="width: 100%; margin-bottom: 2px;">
              <textarea 
                id="fillify-description" 
                placeholder="${getPlaceholder(config.mode)}" 
                style="height: ${config.mode === 'general' ? '200px' : config.mode === 'bugReport' ? '150px' : '150px'};"
                ${!config.hasFormFields ? 'disabled' : ''}
              ></textarea>
            </div>
            
            <!-- Language selector -->
            <div class="fillify-language-selector">
              <div class="fillify-language-flex">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 16px; height: 16px;">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M3.6001 9H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M3.6001 15H20.4001" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 21C13.6569 21 15 16.9706 15 12C15 7.02944 13.6569 3 12 3C10.3432 3 9 7.02944 9 12C9 16.9706 10.3432 21 12 21Z" stroke="#4A5056" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <div class="fillify-selected-language">${selectedLanguageText}</div>
                <select id="fillify-language-select" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                  ${Object.entries(languageMap).map(([value, label]) => `
                    <option value="${value}" ${config.language === value ? 'selected' : ''}>${label}</option>
                  `).join('')}
                </select>
              </div>
            </div>
          </div>

          <!-- Generate button -->
          <button id="fillify-fill-button" type="submit" class="fillify-primary-btn" ${!config.hasFormFields ? 'disabled' : ''}>
            <div class="fillify-button-content" style="display: flex; align-items: center; gap: 6px; position: relative; z-index: 1;">
              <span class="fillify-button-text">Generate</span>
              <svg class="fillify-sparkle-icon" viewBox="0 0 24 24" fill="currentColor">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 4.5a.75.75 0 01.721.544l.813 2.846a3.75 3.75 0 002.576 2.576l2.846.813a.75.75 0 010 1.442l-2.846.813a3.75 3.75 0 00-2.576 2.576l-.813 2.846a.75.75 0 01-1.442 0l-.813-2.846a3.75 3.75 0 00-2.576-2.576l-2.846-.813a.75.75 0 010-1.442l2.846-.813a3.75 3.75 0 002.576-2.576l.813-2.846A.75.75 0 019 4.5zM18 1.5a.75.75 0 01.728.568l.258 1.036c.236.94.97 1.674 1.91 1.91l1.036.258a.75.75 0 010 1.456l-1.036.258c-.94.236-1.674.97-1.91 1.91l-.258 1.036a.75.75 0 01-1.456 0l-.258-1.036a2.625 2.625 0 00-1.91-1.91l-1.036-.258a.75.75 0 010-1.456l1.036-.258a2.625 2.625 0 001.91-1.91l.258-1.036A.75.75 0 0118 1.5zM16.5 15a.75.75 0 01.712.513l.394 1.183c.15.447.5.799.948.948l1.183.395a.75.75 0 010 1.422l-1.183.395c-.447.15-.799.5-.948.948l-.395 1.183a.75.75 0 01-1.422 0l-.395-1.183a1.5 1.5 0 00-.948-.948l-1.183-.395a.75.75 0 010-1.422l1.183-.395c.447-.15.799-.5.948-.948l.395-1.183A.75.75 0 0116.5 15z"></path>
              </svg>
            </div>
            <div class="fillify-animation" style="display: none; position: absolute; border-radius: 100%; animation: ripple 0.6s linear infinite;"></div>
          </button>
        </form>

        <!-- Status message container -->
        <div class="fillify-status-container">
          <div id="fillify-status-message" class="fillify-status-message"></div>
        </div>

        <!-- Confetti Canvas -->
        <canvas class="fillify-confetti-canvas"></canvas>
      </div>
    `;
  }

  private async bindEvents(
    container: HTMLElement,
    loginStatus: LoginStatus,
    userInfo: UserInfo | null,
    config: PopupConfig
  ) {
    // Close button
    const closeBtn = container.querySelector('#fillify-close-btn');
    closeBtn?.addEventListener('click', () => this.hide());

    // Sign in button
    const signinBtn = container.querySelector('#fillify-signin-btn');
    signinBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      this.hide();
    });

    // Skip login
    const skipBtn = container.querySelector('#fillify-skip-login');
    skipBtn?.addEventListener('click', async (e) => {
      e.preventDefault();
      await chrome.runtime.sendMessage({ type: 'setSkipLogin', skip: true });
      this.refreshContent();
    });

    // Avatar click
    const avatar = container.querySelector('#fillify-avatar');
    avatar?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openDashboard' });
      this.hide();
    });

    // Sign in icon click
    const signinIcon = container.querySelector('#fillify-signin-icon');
    signinIcon?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openLoginPage' });
      this.hide();
    });

    // Sign out
    const signoutBtn = container.querySelector('#fillify-signout');
    signoutBtn?.addEventListener('click', async () => {
      try {
        await chrome.runtime.sendMessage({ type: 'signOut' });
        this.refreshContent();
      } catch (error) {
        this.showStatusMessage('Failed to sign out', 'error');
      }
    });

    // Settings button
    const settingsBtn = container.querySelector('#fillify-settings');
    settingsBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openSettings' });
      this.hide();
    });

    // Add project button
    const addProjectBtn = container.querySelector('#fillify-add-project');
    addProjectBtn?.addEventListener('click', () => {
      chrome.runtime.sendMessage({ type: 'openProjectSettings' });
      this.hide();
    });



    // Mode buttons
    const modeButtons = container.querySelectorAll('.fillify-mode-btn');
    modeButtons.forEach(btn => {
      btn.addEventListener('click', async (e) => {
        const target = e.target as HTMLElement;
        const mode = target.getAttribute('data-mode');
        if (mode) {
          await chrome.storage.sync.set({ formify_last_mode: mode });
          this.refreshContent();
        }
      });
    });

    // Language selector
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    languageSelect?.addEventListener('change', async (e) => {
      const target = e.target as HTMLSelectElement;
      await chrome.storage.sync.set({ formify_last_language: target.value });
      
      // Update display text
      const selectedLanguageEl = container.querySelector('.fillify-selected-language');
      if (selectedLanguageEl) {
        const languageMap: Record<string, string> = {
          'auto': 'Auto',
          'zh-CN': '中文（简体）',
          'en': 'English'
          // ... other languages
        };
        selectedLanguageEl.textContent = languageMap[target.value] || target.value;
      }
    });

    // Form submit
    const form = container.querySelector('#fillify-form-config');
    const generateBtn = container.querySelector('#fillify-fill-button');
    
    const handleSubmit = async (e: Event) => {
      e.preventDefault();
      await this.handleGenerate(container);
    };

    form?.addEventListener('submit', handleSubmit);
    generateBtn?.addEventListener('click', handleSubmit);

    // 添加简单的输入事件隔离 - 只针对描述文本框
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    if (descriptionTextarea) {
      descriptionTextarea.addEventListener('keydown', (e) => {
        e.stopPropagation();
      });
      descriptionTextarea.addEventListener('keyup', (e) => {
        e.stopPropagation();
      });
      descriptionTextarea.addEventListener('input', (e) => {
        e.stopPropagation();
      });
    }

    // Update mode background
    this.updateModeBackground(container, config.mode);
  }

  /**
   * 获取当前描述输入内容
   */
  private getCurrentDescription(): string {
    if (!this.ui) return '';
    const descriptionTextarea = this.ui.uiContainer.querySelector('#fillify-description') as HTMLTextAreaElement;
    return descriptionTextarea?.value || '';
  }

  /**
   * 获取当前语言选择
   */
  private getCurrentLanguage(): string {
    if (!this.ui) return '';
    const languageSelect = this.ui.uiContainer.querySelector('#fillify-language-select') as HTMLSelectElement;
    return languageSelect?.value || '';
  }

  /**
   * 获取当前项目选择
   */
  private getCurrentProject(): string {
    if (!this.ui) return '';
    const projectSelect = this.ui.uiContainer.querySelector('#fillify-project-select') as HTMLSelectElement;
    return projectSelect?.value || '';
  }

  /**
   * 恢复用户输入内容
   */
  private restoreUserInputs(description: string, language: string, project: string) {
    if (!this.ui) return;

    // 恢复描述内容
    const descriptionTextarea = this.ui.uiContainer.querySelector('#fillify-description') as HTMLTextAreaElement;
    if (descriptionTextarea && description) {
      descriptionTextarea.value = description;
    }

    // 恢复语言选择
    const languageSelect = this.ui.uiContainer.querySelector('#fillify-language-select') as HTMLSelectElement;
    if (languageSelect && language) {
      languageSelect.value = language;
    }

    // 恢复项目选择
    const projectSelect = this.ui.uiContainer.querySelector('#fillify-project-select') as HTMLSelectElement;
    if (projectSelect && project) {
      projectSelect.value = project;
    }
  }

  private async refreshContent() {
    if (!this.ui) return;

    // 保存当前用户输入的内容
    const currentDescription = this.getCurrentDescription();
    const currentLanguage = this.getCurrentLanguage();
    const currentProject = this.getCurrentProject();

    // Get fresh data
    const [loginStatus, storage, formFields] = await Promise.all([
      chrome.runtime.sendMessage({ type: 'getLoginStatus' }),
      chrome.storage.sync.get(['formify_last_mode', 'formify_projects', 'formify_last_language']),
      this.detectFormFields()
    ]);

    const userInfo = loginStatus.isLoggedIn
      ? await chrome.runtime.sendMessage({ type: 'getUserInfo' })
      : null;

    const config: PopupConfig = {
      mode: storage.formify_last_mode || 'general',
      language: storage.formify_last_language || 'auto',
      projects: storage.formify_projects || [],
      hasFormFields: formFields.length > 0
    };

    // Get container and update content
    if (this.ui) {
      const popup = this.ui.uiContainer.querySelector('.fillify-popup');
      if (popup) {
        popup.innerHTML = this.getPopupHTML(loginStatus, userInfo, config);
        this.bindEvents(this.ui.uiContainer, loginStatus, userInfo, config);

        // 恢复用户输入的内容
        this.restoreUserInputs(currentDescription, currentLanguage, currentProject);
      }
    }
  }

  private async handleGenerate(container: HTMLElement) {
    
    const generateBtn = container.querySelector('#fillify-fill-button') as HTMLButtonElement;
    const btnText = container.querySelector('.fillify-button-text') as HTMLElement;
    const sparkleIcon = container.querySelector('.fillify-sparkle-icon') as HTMLElement;
    const loadingAnimation = container.querySelector('.fillify-animation') as HTMLElement;
    const descriptionTextarea = container.querySelector('#fillify-description') as HTMLTextAreaElement;
    const languageSelect = container.querySelector('#fillify-language-select') as HTMLSelectElement;
    const projectSelect = container.querySelector('#fillify-project-select') as HTMLSelectElement;

    if (!generateBtn || !btnText || !sparkleIcon || !loadingAnimation || !descriptionTextarea) {
      console.error('Required elements not found in popup');
      return;
    }

    const description = descriptionTextarea.value.trim();
    if (!description) {
      this.showStatusMessage('Please enter a description', 'error');
      return;
    }

    try {
      // Show loading state
      generateBtn.disabled = true;
      generateBtn.classList.add('loading');
      btnText.textContent = 'Generating...';
      sparkleIcon.classList.add('hidden');
      loadingAnimation.style.display = 'block';

      // Get current mode
      const storage = await chrome.storage.sync.get(['formify_last_mode']);
      const currentMode = storage.formify_last_mode || 'general';

      // Detect form fields
      const formFields = await this.detectFormFields();
      if (!formFields.length) {
        throw new Error('No form fields detected on this page');
      }

      // Show generating effect
      this.showGeneratingEffect();

      // Send AI request
      const aiResponse = await chrome.runtime.sendMessage({
        type: 'aiRequest',
        prompt: description,
        options: {
          mode: currentMode,
          projectId: projectSelect?.value || '',
          language: languageSelect?.value || 'auto',
          description: description,
          formFields: formFields.map(field => ({
            type: field.type,
            id: field.id,
            name: field.name,
            placeholder: field.placeholder,
            label: field.label
          }))
        }
      });

      console.log('[InPagePopup] AI Response received:', aiResponse);
      console.log('[InPagePopup] AI Response data structure:', {
        hasData: !!aiResponse?.data,
        dataKeys: aiResponse?.data ? Object.keys(aiResponse.data) : [],
        hasReasoning: !!aiResponse?.data?.reasoning,
        reasoningLength: aiResponse?.data?.reasoning?.length || 0,
        reasoning: aiResponse?.data?.reasoning,
        isStreaming: !!aiResponse?.streaming
      });

      if (!aiResponse?.success) {
        throw new Error(aiResponse?.error || 'Failed to generate content');
      }

      // 检查是否为流式响应
      if (aiResponse.streaming) {
        console.log('[InPagePopup] Streaming response detected, waiting for completion...');
        // 流式响应，等待 streamingComplete 消息
        // 不需要在这里处理，由 handleStreamingComplete 处理
        return;
      }

      // 非流式响应，直接处理
      if (!aiResponse.data) {
        throw new Error('No data received from AI response');
      }

      // Fill form
      await this.fillForm(aiResponse.data.data || aiResponse.data);

    // Success state
      generateBtn.classList.remove('loading');
      generateBtn.classList.add('success');
      btnText.textContent = 'Finish';
      loadingAnimation.style.display = 'none';
      sparkleIcon.classList.remove('hidden');



      // Show confetti
      this.createConfetti(container);

      this.showStatusMessage('Form filled successfully!', 'success');

      // 移除自动关闭弹窗的逻辑，让用户手动关闭
      // setTimeout(() => {
      //   this.hide();
      // }, 1500);

    } catch (error) {
      console.error('Error in handleGenerate:', error);
      this.showStatusMessage(error instanceof Error ? error.message : 'An error occurred', 'error');
    } finally {
      // Restore button state if not success
      if (!generateBtn.classList.contains('success')) {
        generateBtn.disabled = false;
        generateBtn.classList.remove('loading');
        btnText.textContent = 'Generate';
        sparkleIcon.classList.remove('hidden');
        loadingAnimation.style.display = 'none';
        

      }

      // Clear generating effect
      this.removeGeneratingEffect();
    }
  }

  private async detectFormFields(): Promise<FormField[]> {
    // 直接通过 window 对象获取检测到的表单字段
    // 这是一个临时解决方案，我们在页面上暴露一个全局函数
    return new Promise((resolve) => {
      // 使用 postMessage 与 content script 通信
      const messageId = `detect-fields-${Date.now()}`;
      
      const handleMessage = (event: MessageEvent) => {
        if (event.source === window && event.data?.type === 'DETECT_FIELDS_RESPONSE' && event.data?.id === messageId) {
          window.removeEventListener('message', handleMessage);
          resolve(event.data.fields || []);
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      // 发送检测请求
      window.postMessage({
        type: 'DETECT_FIELDS_REQUEST',
        id: messageId
      }, '*');
      
      // 超时处理
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        resolve([]);
      }, 1000);
    });
  }

  private showGeneratingEffect() {
    // Send message to content script to show generating effect
    chrome.runtime.sendMessage({ type: 'showGeneratingEffect' });
  }

  private removeGeneratingEffect() {
    // Send message to content script to remove generating effect
    chrome.runtime.sendMessage({ type: 'removeGeneratingEffect' });
  }

  private async fillForm(formData: any) {
    // Send message to content script to fill form
    return new Promise<void>((resolve, reject) => {
      chrome.runtime.sendMessage({
        type: 'fillForm',
        data: formData
      }, (response) => {
        if (response?.success) {
          resolve();
        } else {
          reject(new Error(response?.error || 'Failed to fill form'));
        }
      });
    });
  }

  private showStatusMessage(message: string, type: 'info' | 'error' | 'success' = 'info') {
    if (!this.ui) return;
    
    const container = this.ui.uiContainer;
    const statusMessage = container?.querySelector('#fillify-status-message') as HTMLElement;
    
    if (statusMessage) {
      statusMessage.textContent = message;
      statusMessage.className = `fillify-status-message show ${type}`;
      
      // Auto-hide
      setTimeout(() => {
        statusMessage.classList.remove('show');
      }, 3000);
    }
  }

  private updateModeBackground(container: HTMLElement, mode: string) {
    // 计算背景的transform值
    let transformValue = 'translateX(0)';
    if (mode === 'email') {
      transformValue = 'translateX(calc(100% + 4px))';
    } else if (mode === 'bugReport') {
      transformValue = 'translateX(calc(200% + 8px))';
    }

    // 获取或创建样式标签
    let modeStyleTag = container.querySelector('#fillify-mode-style') as HTMLStyleElement;
    if (!modeStyleTag) {
      modeStyleTag = document.createElement('style');
      modeStyleTag.id = 'fillify-mode-style';
      container.appendChild(modeStyleTag);
    }
    
    // 更新样式以实现平滑的背景切换动画
    modeStyleTag.textContent = `
      .fillify-mode-buttons::after {
        transform: ${transformValue} !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      }
      
      /* 确保模式按钮的激活状态正确显示 */
      .fillify-mode-btn.active {
        color: #2962FF !important;
        font-weight: 500 !important;
      }
      
      /* 添加按钮悬停效果的细微调整 */
      .fillify-mode-btn:not(.active):hover {
        background: rgba(41, 98, 255, 0.1) !important;
        transform: scale(1.02) !important;
      }
      
      .fillify-mode-btn.active:hover {
        background: transparent !important;
        transform: none !important;
      }
      
      /* 增强按钮点击动画 */
      .fillify-mode-btn:active {
        transform: scale(0.97) !important;
        transition: transform 0.1s ease !important;
      }
    `;
    
    // 更新按钮的active状态
    const modeButtons = container.querySelectorAll('.fillify-mode-btn');
    modeButtons.forEach(button => {
      const buttonMode = button.getAttribute('data-mode');
      if (buttonMode === mode) {
        button.classList.add('active');
      } else {
        button.classList.remove('active');
      }
    });
  }

  private setupDragFunctionality(container: HTMLElement) {
    const popup = container.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    const handleMouseDown = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      // Exclude interactive elements
      if (target.tagName === 'BUTTON' || 
          target.tagName === 'INPUT' || 
          target.tagName === 'TEXTAREA' || 
          target.tagName === 'SELECT' ||
          target.closest('button, input, textarea, select, .fillify-menu-popover')) {
        return;
      }
      
      e.preventDefault();
      
      this.dragStartX = e.clientX;
      this.dragStartY = e.clientY;
      
      const rect = popup.getBoundingClientRect();
      this.popupStartX = rect.left;
      this.popupStartY = rect.top;
      
      this.isLongPressTriggered = false;
      
      // Long press timer - 优化为和原版相同的300ms
      this.longPressTimer = window.setTimeout(() => {
        this.isLongPressTriggered = true;
        this.startDragging(popup);
      }, 300);
      
      document.addEventListener('mousemove', this.handleMouseMove.bind(this));
      document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    };

    popup.addEventListener('mousedown', handleMouseDown);
  }

  private handleMouseMove = (e: MouseEvent) => {
    if (this.longPressTimer && !this.isLongPressTriggered) {
      const moveThreshold = 5;
      if (Math.abs(e.clientX - this.dragStartX) > moveThreshold || 
          Math.abs(e.clientY - this.dragStartY) > moveThreshold) {
        clearTimeout(this.longPressTimer);
        this.longPressTimer = null;
      }
    }
    
    if (this.isDragging && this.ui) {
      e.preventDefault();
      e.stopPropagation();
      
      const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
      if (!popup) return;
      
      const deltaX = e.clientX - this.dragStartX;
      const deltaY = e.clientY - this.dragStartY;
      
      let newX = this.popupStartX + deltaX;
      let newY = this.popupStartY + deltaY;
      
      // Boundary checks
      const rect = popup.getBoundingClientRect();
      const maxX = window.innerWidth - rect.width;
      const maxY = window.innerHeight - rect.height;
      
      newX = Math.max(0, Math.min(newX, maxX));
      newY = Math.max(0, Math.min(newY, maxY));
      
      popup.style.left = `${newX}px`;
      popup.style.top = `${newY}px`;
      popup.style.right = 'auto';
      popup.style.bottom = 'auto';
    }
  };

  private handleMouseUp = () => {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
    
    if (this.isDragging) {
      this.stopDragging();
    }
    
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
  };

  private startDragging(popup: HTMLElement) {
    this.isDragging = true;
    popup.style.cursor = 'grabbing';
    popup.style.transform = 'scale(1.02)';
    popup.style.boxShadow = '0 25px 70px rgba(0, 0, 0, 0.4)';
    popup.style.transition = 'transform 0.1s ease, box-shadow 0.1s ease';
    
    document.body.style.userSelect = 'none';
  }

  private stopDragging() {
    if (!this.ui) return;
    
    const popup = this.ui.uiContainer.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;
    
    this.isDragging = false;
    popup.style.cursor = '';
    popup.style.transform = '';
    popup.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.3)';
    popup.style.transition = 'transform 0.15s ease, box-shadow 0.15s ease';
    
    document.body.style.userSelect = '';
    
    setTimeout(() => {
      if (popup) {
        popup.style.transition = '';
      }
    }, 150);
  }

  private createConfetti(container: HTMLElement) {
    const canvas = container.querySelector('.fillify-confetti-canvas') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const popup = container.querySelector('.fillify-popup') as HTMLElement;
    if (!popup) return;

    const rect = popup.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Confetti implementation similar to the original
    const confettiConfig = {
      confettiCount: 20,
      sequinCount: 10,
      colors: [
        { front: '#7b5cff', back: '#6245e0' },
        { front: '#b3c7ff', back: '#8fa5e5' },
        { front: '#5c86ff', back: '#345dd1' }
      ]
    };

    let confetti: any[] = [];

    // Simple confetti implementation
    for (let i = 0; i < confettiConfig.confettiCount; i++) {
      confetti.push({
        x: canvas.width / 2,
        y: canvas.height / 2,
        vx: (Math.random() - 0.5) * 10,
        vy: Math.random() * -10 - 5,
        color: confettiConfig.colors[Math.floor(Math.random() * confettiConfig.colors.length)].front,
        size: Math.random() * 6 + 4
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      confetti.forEach((particle, index) => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.vy += 0.3; // gravity
        
        ctx.fillStyle = particle.color;
        ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        
        if (particle.y > canvas.height + 100) {
          confetti.splice(index, 1);
        }
      });
      
      if (confetti.length > 0) {
        this.animationFrame = requestAnimationFrame(animate);
      }
    };
    
    animate();
  }

  private cleanup() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }






}
